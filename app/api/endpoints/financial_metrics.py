from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from typing import List
from ...database import get_db
from ...models.models import merchant_metrics
from ...models.probe_model import probe_financials, probe_merchant
from datetime import datetime

def format_indian_currency(value):
    if value is None:
        return None
    value = float(value)
    if value < 0:
        value = abs(value)
        prefix = "-₹"
    else:
        prefix = "₹"
    
    # Convert to string and split by decimal
    str_value = f"{value:,.0f}"
    parts = str_value.split(".")
    num = parts[0]
    
    # Convert to Indian format
    last_three = num[-3:]
    other_numbers = num[:-3]
    if other_numbers:
        formatted = other_numbers[::-1].replace(",", "")  # Remove existing commas
        formatted = ",".join(formatted[i:i+2] for i in range(0, len(formatted), 2))
        formatted = formatted[::-1] + "," + last_three
    else:
        formatted = last_three
    
    return prefix + formatted

router = APIRouter()

@router.get("/merchants/{merchant_id}/financial-metrics")
def get_financial_metrics(merchant_id: str, db: Session = Depends(get_db)):
    try:
        # Get latest financial data from probe_financials
        latest_financials = db.query(probe_financials).filter(
            probe_financials.merchant_id == merchant_id,
            probe_financials.nature == "STANDALONE"
        ).order_by(desc(probe_financials.year)).first()

        if not latest_financials:
            raise HTTPException(status_code=404, detail="Financial data not found")

        # Get previous year's data for revenue growth calculation
        prev_year_financials = db.query(probe_financials).filter(
            probe_financials.merchant_id == merchant_id,
            probe_financials.nature == "STANDALONE",
            probe_financials.year < latest_financials.year
        ).order_by(desc(probe_financials.year)).first()

        # Get merchant metrics for each required metric type
        fixed_cost_ratio = db.query(merchant_metrics).filter(
            merchant_metrics.merchant_id == merchant_id,
            merchant_metrics.metric_type == 'FIXED_COST_COVERAGE_RATIO'
        ).order_by(desc(merchant_metrics.year)).first()

        beneish_score = db.query(merchant_metrics).filter(
            merchant_metrics.merchant_id == merchant_id,
            merchant_metrics.metric_type == 'BENEISH_M_SCORE'
        ).order_by(desc(merchant_metrics.year)).first()

        altman_score = db.query(merchant_metrics).filter(
            merchant_metrics.merchant_id == merchant_id,
            merchant_metrics.metric_type == 'ALTMAN_Z_SCORE'
        ).order_by(desc(merchant_metrics.year)).first()

        recovery_rate = db.query(merchant_metrics).filter(
            merchant_metrics.merchant_id == merchant_id,
            merchant_metrics.metric_type == 'RECOVERY_RATE_ON_DEFAULT'
        ).order_by(desc(merchant_metrics.year)).first()

        # Get merchant data for sum of charges
        merchant_data = db.query(probe_merchant).filter(
            probe_merchant.merchant_id == merchant_id
        ).first()

        # Calculate metrics
        metrics = []

        # Revenue Growth
        if prev_year_financials and latest_financials.net_revenue and prev_year_financials.net_revenue:
            revenue_growth = ((latest_financials.net_revenue - prev_year_financials.net_revenue) / prev_year_financials.net_revenue) * 100
            color = "text-green-500" if revenue_growth > 0 else "text-red-500"
            metrics.append({
                "label": "Revenue Growth", 
                "value": f"{round(revenue_growth, 2)}%",
                "icon": f"<TrendingUp className=\"h-5 w-5 {color}\" />"
            })

        # # Gross Profit Margin
        # if latest_financials.net_revenue:
        #     cost_of_goods = (latest_financials.total_cost_of_materials_consumed or 0) + \
        #                   (latest_financials.total_purchases_of_stock_in_trade or 0) + \
        #                   (latest_financials.total_changes_in_inventories_or_finished_goods or 0)
        #     gross_profit_margin = ((latest_financials.net_revenue - cost_of_goods) / latest_financials.net_revenue) * 100
        #     color = "text-green-500" if gross_profit_margin > 20 else "text-red-500"
        #     metrics.append({
        #         "label": "Gross Profit Margin", 
        #         "value": f"{round(gross_profit_margin, 2)}%",
        #         "icon": f"<Percent className=\"h-5 w-5 {color}\" />"
        #     })

        # Current Ratio
        if latest_financials.total_current_assets and latest_financials.total_current_liabilities:
            current_ratio = latest_financials.total_current_assets / latest_financials.total_current_liabilities
            color = "text-green-500" if current_ratio > 1 else "text-red-500"
            metrics.append({
                "label": "Current Ratio", 
                "value": round(current_ratio, 2),
                "icon": f"<Scale className=\"h-5 w-5 {color}\" />"
            })

        # Networth
        if latest_financials.given_assets_total and latest_financials.total_current_liabilities and latest_financials.total_non_current_liabilities:
            networth = latest_financials.given_assets_total - latest_financials.total_current_liabilities - latest_financials.total_non_current_liabilities
            color = "text-green-500" if networth > 0 else "text-red-500"
            metrics.append({
                "label": "Net Worth", 
                "value": format_indian_currency(networth),
                "icon": f"<Wallet className=\"h-5 w-5 {color}\" />"
            })

        # Working Capital
        if latest_financials.total_current_assets and latest_financials.total_current_liabilities:
            working_capital = latest_financials.total_current_assets - latest_financials.total_current_liabilities
            color = "text-green-500" if working_capital > 0 else "text-red-500"
            metrics.append({
                "label": "Working Capital", 
                "value": format_indian_currency(working_capital),
                "icon": f"<Coins className=\"h-5 w-5 {color}\" />"
            })

        # Fixed Cost Coverage Ratio
        if fixed_cost_ratio and fixed_cost_ratio.metric_value:
            value = float(fixed_cost_ratio.metric_value)
            color = "text-green-500" if value > 1 else "text-red-500"
            metrics.append({
                "label": "Fixed Cost Coverage",
                "value": round(value, 2),
                "icon": f"<Calculator className=\"h-5 w-5 {color}\" />"
            })

        # Sum of amount (open_charges)
        if merchant_data and merchant_data.sum_of_charges:
            value = float(merchant_data.sum_of_charges)
            color = "text-red-500" if value > 0 else "text-green-500"
            metrics.append({
                "label": "Open Charges",
                "value": format_indian_currency(value),
                "icon": f"<CreditCard className=\"h-5 w-5 {color}\" />"
            })

        # Beneish M-Score
        if beneish_score and beneish_score.metric_value:
            value = float(beneish_score.metric_value)
            color = "text-red-500" if value > -2.22 else "text-green-500"
            metrics.append({
                "label": "Beneish M-Score",
                "value": round(value, 2),
                "icon": f"<AlertTriangle className=\"h-5 w-5 {color}\" />"
            })

        # Altman Z-Score
        if altman_score and altman_score.metric_value:
            value = float(altman_score.metric_value)
            color = "text-green-500" if value > 2.99 else "text-red-500"
            metrics.append({
                "label": "Altman Z-Score",
                "value": round(value, 2),
                "icon": f"<Activity className=\"h-5 w-5 {color}\" />"
            })

        # Recovery in case of Default
        if recovery_rate and recovery_rate.metric_value:
            value = float(recovery_rate.metric_value)
            color = "text-green-500" if value > 0.5 else "text-red-500"
            metrics.append({
                "label": "Recovery Rate",
                "value": f"{round(value * 100, 2)}%",
                "icon": f"<Shield className=\"h-5 w-5 {color}\" />"
            })

        return metrics

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 