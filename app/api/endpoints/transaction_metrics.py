from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from ...database import get_db

router = APIRouter()

@router.get("/merchants/{merchant_id}/transaction-metrics")
def get_transaction_metrics(merchant_id: str, db: Session = Depends(get_db)):
    try:
        # Hardcoded transaction metrics
        metrics = []

        # Charge back rates
        charge_back_rate = 1.0
        color = "text-green-500" if charge_back_rate < 2 else "text-red-500"
        metrics.append({
            "label": "Charge Back Rate",
            "value": f"{charge_back_rate}%",
            "icon": f"<CreditCard className=\"h-5 w-5 {color}\" />"
        })

        # Average Transaction Value
        avg_transaction = 10000
        color = "text-green-500" if avg_transaction > 5000 else "text-blue-500"
        metrics.append({
            "label": "Average Transaction Value",
            "value": f"₹{avg_transaction:,}",
            "icon": f"<IndianRupee className=\"h-5 w-5 {color}\" />"
        })

        # Transaction Frequency
        daily_transactions = 500
        color = "text-green-500" if daily_transactions > 300 else "text-blue-500"
        metrics.append({
            "label": "Daily Transactions",
            "value": f"{daily_transactions:,}",
            "icon": f"<Activity className=\"h-5 w-5 {color}\" />"
        })

        # Seasonal Effect
        seasonal_effect = "Yes"
        color = "text-blue-500"
        metrics.append({
            "label": "Seasonal Effect",
            "value": seasonal_effect,
            "icon": f"<Calendar className=\"h-5 w-5 {color}\" />"
        })

        # Unique Customers
        unique_customers = 500000
        color = "text-green-500" if unique_customers > 100000 else "text-blue-500"
        metrics.append({
            "label": "Unique Customers",
            "value": f"{unique_customers:,}",
            "icon": f"<Users className=\"h-5 w-5 {color}\" />"
        })

        return metrics

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 