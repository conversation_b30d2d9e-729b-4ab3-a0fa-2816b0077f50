from datetime import datetime
from sqlalchemy import <PERSON>umn, Integer, String, Float, DateTime, Boolean, ForeignKey, JSON, Date, Numeric, Text, UUID, DECIMAL, ARRAY, Enum, BigInteger, TIMESTAMP, VARCHAR
from sqlalchemy.orm import relationship as sqlalchemy_relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid
from uuid import uuid4
from ..database import Base

class ChatMessages(Base):
    __tablename__ = 'chat_messages'
    __table_args__ = {'schema': 'public'}

    message_id = Column(String(255), primary_key=True)
    chat_id = Column(String(255), index=True, nullable=False)
    merchant_id = Column(String(255), index=True, nullable=False)
    user_id = Column(String(255), index=True, nullable=False)
    visualization = Column(Boolean, nullable=False, default=False) # only true when editing a dashboard visualization
    visualization_id = Column(String(255), nullable=True) # id of the visualization being edited, have to be non-null when visualization is true
    sender = Column(String(255), nullable=False)
    message = Column(JSONB, nullable=False)
    status = Column(String(255), nullable=False)
    steps = Column(JSONB, nullable=False)
    created_at = Column(DateTime, nullable=False, default=datetime.now)
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)
    meta_data = Column(JSONB, nullable=True)

class ActiveChatIds(Base):
    __tablename__ = 'active_chatids'
    __table_args__ = {'schema': 'public'}

    chat_id = Column(String(255), primary_key=True)  # Changed from UUID to String
    user_id = Column(String(255), nullable=False, index=True)
    merchant_id = Column(String(255), nullable=False, index=True)
    has_visualization = Column(Boolean, default=False) # Indicate if the message has a visualization
    chat_title = Column(String, nullable=True)
    visualization_id = Column(String, nullable=True) # if the message has a visualization, this will be the id of the visualization and can't be null
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class GraphSpecs(Base):
    __tablename__ = 'visualizations'
    __table_args__ = {'schema': 'public'}

    visualization_id = Column(String(255), primary_key=True)  # changed from UUID to String
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    dashboard_id = Column(String(255), nullable=True)
    chat_id = Column(String(255), nullable=True)  # changed from UUID to String
    config = Column(JSONB, nullable=False)
    code_type = Column(String(50), nullable=False)  # Type of code used for the graph (e.g., 'python', 'sql')
    code = Column(Text, nullable=True)  # Python code for the graph, if applicable
    query = Column(Text, nullable=True)  # SQL query for the graph, if applicable
    created_by = Column(String(255), nullable=False)  # User who created the visualization
    created_at = Column(DateTime, nullable=False, default=datetime.now)
    updated_by = Column(String(255), nullable=False)  # User who last updated the visualization
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)

class Dashboard(Base):
    __tablename__ = 'dashboards'
    __table_args__ = {'schema': 'public'}

    dashboard_id = Column(String(255), primary_key=True)  
    customer_id = Column(String(255), nullable=True)
    organization_id = Column(String(255), nullable=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    created_by = Column(String(255), nullable=False)  # User who created the dashboard
    created_at = Column(DateTime, nullable=False, default=datetime.now)
    dash_type = Column(String(50), nullable=False, default='system')