import os
import sys
from pathlib import Path
from typing import List, Dict
import json
from langchain_openai import ChatOpenAI
from langchain_core.prompts import Chat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MessagesPlaceholder
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from langchain.chains.conversation.memory import ConversationBufferWindowMemory
from langchain.agents import AgentExecutor, create_openai_functions_agent
from langchain_core.tools import Tool
from langchain.agents.agent import Agent
from langchain.agents.format_scratchpad import format_to_openai_function_messages
from dotenv import load_dotenv
import traceback
import logging
import pprint
import re
from decimal import Decimal
from datetime import date, datetime
import uuid

# Set up detailed logging
logging.basicConfig(
    level=logging.ERROR,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Add the project root to Python path
project_root = str(Path(__file__).resolve().parent.parent.parent.parent.parent)
sys.path.append(project_root)

# Import the actual functions
from app.api.endpoints.transaction_metrics import get_transaction_metrics as get_txn_metrics
from app.api.endpoints.financial_metrics import get_financial_metrics as get_fin_metrics
from app.database import get_db

# Import the other direct functions
from app.routers.creditDashboardInternal import (
    get_about_company_internal,
    get_about_industry_internal,
    get_company_metrics_internal,
    get_risk_metrics_internal
)
from app.routers.merchant_red_flags import get_merchant_red_flags_internal

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return str(obj)
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        return super().default(obj)

class MetricsGPT:
    def __init__(self):
        try:
            self.openai_api_key = os.getenv('OPENAI_API_KEY')
            if not self.openai_api_key:
                raise ValueError("OPENAI_API_KEY not found in environment variables")
            
            self.model = 'gpt-4-turbo'
            self.llm = ChatOpenAI(
                api_key=self.openai_api_key,
                model=self.model,
                temperature=0.2
            )
            
            self.example_code = self._read_guide()

            self.system_prompt = """You are a Metrics Analysis Assistant that evaluates merchants by retrieving and explaining key financial, transaction, and risk metrics. You have access to specialized tools to provide detailed, data-driven insights. Use the most relevant tools based on the user's request to generate a concise, clear, and actionable summary.

You have access to the following tools:

get_transaction_metrics – Retrieve transaction patterns, chargeback rates, and average transaction values.

get_financial_metrics – Retrieve financial health metrics, including revenue growth, current ratio, working capital, and more.

get_about_the_company – Retrieve basic company profile and overview.

get_about_industry_and_risk – Retrieve industry-specific details and associated risk factors.

get_company_metrics – Retrieve comprehensive company-specific metrics.

get_risk_metrics – Retrieve company-level risk assessment data.

get_merchant_red_flags – Retrieve red flags associated with the merchant using specified rule types.

Instructions:

Determine the user's intent (financial, transaction, risk, or profile insight).

Call the appropriate tools based on the query.

Present your findings in a clear and professional manner, including interpretation of metrics where helpful.

If multiple dimensions (e.g., financial + risk) are requested, combine tool outputs into a holistic summary.

Goal: Deliver actionable insights on merchant performance and health using real-time data from the tools provided."""

            self.tools = self._setup_tools()

            self.memory = ConversationBufferWindowMemory(
                k=5,
                memory_key="chat_history",
                return_messages=True
            )

            # Generate tool descriptions once
            tool_descriptions = "\n".join([f"{tool.name}: {tool.description}" for tool in self.tools])
            tool_names = ", ".join([tool.name for tool in self.tools])
            
            # Debug output
            print(f"Tool names: {tool_names}")
            print(f"Tool descriptions: {tool_descriptions}")
            
            system_content = f"{self.system_prompt}\n\nTools: {{tools}}\nTool Names: {{tool_names}}"
            print(f"System message template: {system_content}")

            # Create the prompt template for the ReAct agent
            self.prompt = ChatPromptTemplate.from_messages([
                ("system", self.system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{input}"),
                MessagesPlaceholder(variable_name="agent_scratchpad"),
            ])
            
            # Format the template variables
            formatted_prompt = self.prompt.partial(
                tools=tool_descriptions,
                tool_names=tool_names
            )
            
            print(f"Prompt template: {formatted_prompt}")
            print(f"Input variables: {formatted_prompt.input_variables}")
            
            # Define a custom formatter function
            def format_steps_to_messages(intermediate_steps):
                messages = []
                for action, observation in intermediate_steps:
                    messages.append(AIMessage(content=f"I'll use the {action.tool} tool with input: {action.tool_input}"))
                    messages.append(HumanMessage(content=f"Observation: {observation}"))
                return messages
            
            print(f"Prompt: {formatted_prompt}")

            # Create the OpenAI Functions agent (this returns a Runnable)
            self.agent = create_openai_functions_agent(llm=self.llm, tools=self.tools, prompt=self.prompt)

            print(f"Agent: {self.agent}")

            # Wrap it in an AgentExecutor
            self.agent_executor = AgentExecutor(
                agent=self.agent, 
                tools=self.tools,
                memory=self.memory,
                verbose=True,
                handle_parsing_errors=True,
                return_intermediate_steps=True
            )
            
            print(f"Agent executor created: {self.agent_executor}")
            
            print("MetricsGPT initialized successfully")
            
        except Exception as e:
            logging.error(f"Error initializing MetricsGPT: {str(e)}\n{traceback.format_exc()}")
            raise

    def _read_guide(self) -> str:
        try:
            guide_path = Path(__file__).parent / "metrics_guide.py"
            with open(guide_path, 'r') as file:
                return file.read()
        except Exception as e:
            print(f"Error reading guide: {str(e)}")
            return "# Example code not available"

    def get_transaction_metrics(self, merchant_id: str) -> str:
        try:
            print(f"Getting transaction metrics for merchant {merchant_id}")
            db = next(get_db())
            metrics = get_txn_metrics(merchant_id, db)
            if isinstance(metrics, str):
                try:
                    metrics = json.loads(metrics)
                except:
                    return metrics
            return json.dumps(metrics, ensure_ascii=False, cls=CustomJSONEncoder)
        except Exception as e:
            error_msg = f"Error getting transaction metrics: {str(e)}"
            logger.error(error_msg)
            return error_msg
        finally:
            db.close()

    def get_financial_metrics(self, merchant_id: str):
        try:
            print(f"Getting financial metrics for merchant {merchant_id}")
            db = next(get_db())
            metrics = get_fin_metrics(merchant_id, db)
            if isinstance(metrics, str):
                try:
                    return json.loads(metrics)
                except:
                    return metrics
            return metrics
        except Exception as e:
            error_msg = f"Error getting financial metrics: {str(e)}"
            logger.error(error_msg)
            return error_msg
        finally:
            db.close()
            
    # Direct function calls for additional tools
    def get_about_the_company(self, merchant_id: str) -> str:
        try:
            print(f"Getting company information for merchant {merchant_id}")
            db = next(get_db())
            data = get_about_company_internal(merchant_id=merchant_id, db=db)
            return json.dumps(data, ensure_ascii=False, cls=CustomJSONEncoder)
        except Exception as e:
            error_msg = f"Error getting company information: {str(e)}"
            logger.error(error_msg)
            return error_msg
        finally:
            db.close()
    
    def get_about_industry_and_risk(self, merchant_id: str) -> str:
        try:
            print(f"Getting industry and risk information for merchant {merchant_id}")
            db = next(get_db())
            data = get_about_industry_internal(merchant_id=merchant_id, db=db)
            return json.dumps(data, ensure_ascii=False, cls=CustomJSONEncoder)
        except Exception as e:
            error_msg = f"Error getting industry and risk information: {str(e)}"
            logger.error(error_msg)
            return error_msg
        finally:
            db.close()
    
    def get_company_metrics(self, merchant_id: str) -> str:
        try:
            print(f"Getting company metrics for merchant {merchant_id}")
            db = next(get_db())
            data = get_company_metrics_internal(merchant_id=merchant_id, db=db)
            return json.dumps(data, ensure_ascii=False, cls=CustomJSONEncoder)
        except Exception as e:
            error_msg = f"Error getting company metrics: {str(e)}"
            logger.error(error_msg)
            return error_msg
        finally:
            db.close()
    
    def get_risk_metrics(self, merchant_id: str) -> str:
        try:
            print(f"Getting risk metrics for merchant {merchant_id}")
            db = next(get_db())
            data = get_risk_metrics_internal(merchant_id=merchant_id, db=db)
            return json.dumps(data, ensure_ascii=False, cls=CustomJSONEncoder)
        except Exception as e:
            error_msg = f"Error getting risk metrics: {str(e)}"
            logger.error(error_msg)
            return error_msg
        finally:
            db.close()

    def get_merchant_red_flags(self, merchant_id: str, query: str) -> str:
        try:
            print(f"Getting red flags for merchant {merchant_id}")
            db = next(get_db())
            
            # Ask LLM to determine the appropriate rule type
            prompt = f"""Map this query: '{query}' to exactly one of these DB values (return ONLY the matching DB value, nothing else):
            - insolvency_external_audit (for audit related flags)
            - insolvency_external_executive (for executive management related flags)
            - insolvency_external_disclosures (for disclosure related flags)
            - insolvency_external_financial (for financial statement related flags)
            - insolvency_external_legal (for legal issues related flags)
            - insolvency_external_operational (for operational issues related flags)
            - insolvency_external_annualReport (for annual report related flags)
            - insolvency_external_industry (for industry specific flags)
            - insolvency_external_brand (for brand/reputation related flags)
            - insolvency_financial_financialStatements (for financial statement analysis flags)
            - insolvency_financial_financialMetrics (for financial metrics related flags)
            - transaction_monitoring (for transaction related flags)
            - insolvency_overview_pdAnalysis (for probability of default analysis flags)
            - insolvency_overview_company (for company overview related flags)
            - insolvency_overview_industry (for industry overview flags)
            - insolvency_overview_transactionMetrics (for transaction metrics flags)"""
            
            db_rule_type = self.llm.invoke(prompt).content.strip()
            print(f"DB rule type: {db_rule_type}")
            
            data = get_merchant_red_flags_internal(merchant_id=uuid.UUID(merchant_id), db=db, rule_type=db_rule_type)
            
            # Convert UUIDs to strings in the response
            for item in data:
                if 'id' in item:
                    item['id'] = str(item['id'])
                if 'merchant_id' in item:
                    item['merchant_id'] = str(item['merchant_id'])
            
            return json.dumps(data, ensure_ascii=False, cls=CustomJSONEncoder)
        except Exception as e:
            error_msg = f"Error getting merchant red flags: {str(e)}"
            logger.error(error_msg)
            return error_msg
        finally:
            db.close()

    def _setup_tools(self) -> List[Tool]:
        tools = [
            Tool(
                name="get_transaction_metrics",
                func=self.get_transaction_metrics,
                description="Get transaction metrics for a merchant by providing the merchant_id. Returns metrics about transaction patterns, charge back rates, etc."
            ),
            Tool(
                name="get_financial_metrics",
                func=self.get_financial_metrics,
                description="Get financial metrics for a merchant by providing the merchant_id. Returns metrics about financial health including revenue growth, current ratio, working capital, etc."
            ),
            Tool(
                name="get_about_the_company",
                func=self.get_about_the_company,
                description="Get basic information about the company by providing the merchant_id. Returns details like company name, description, etc."
            ),
            Tool(
                name="get_about_industry_and_risk",
                func=self.get_about_industry_and_risk,
                description="Get information about the company's industry and associated risks by providing the merchant_id. Returns industry classification, risk factors, etc."
            ),
            Tool(
                name="get_company_metrics",
                func=self.get_company_metrics,
                description="Get detailed metrics about the company by providing the merchant_id. Returns comprehensive business metrics and KPIs."
            ),
            Tool(
                name="get_risk_metrics",
                func=self.get_risk_metrics,
                description="Get risk assessment metrics for the company by providing the merchant_id. Returns risk scores, exposure metrics, etc."
            ),
            Tool(
                name="get_merchant_red_flags",
                func=self.get_merchant_red_flags,
                description="Retrieve red flags associated with the merchant using specified rule types."
            )
        ]
        print(f"Tools set up: {[tool.name for tool in tools]}")
        return tools

    def _identify_metrics(self, query: str) -> List[str]:
        """Use LLM to identify which metrics to fetch"""
        prompt = f"""Identify which metrics are requested in this query. Return only the metric names from this list:
        - financial
        - company_metrics
        - transaction
        - company_details
        - industry
        - risk_metrics
        - red_flags
        Query: {query}
        Return as comma separated list without spaces. Example: financial,company_metrics"""
        
        response = self.llm.invoke(prompt).content
        return [metric.strip() for metric in response.split(',')]

    def run(self, query: str) -> str:
        try:
            print(f"Running query: {query}")
            
            # Extract merchant ID if present in query
            merchant_id = re.search(r'[a-f0-9-]{36}', query)
            if merchant_id:
                merchant_id = merchant_id.group(0)
                responses = []
                
                # Use LLM to identify requested metrics
                metrics_map = {
                    "financial": self.get_financial_metrics,
                    "company_metrics": self.get_company_metrics,
                    "transaction": self.get_transaction_metrics,
                    "company_details": self.get_about_the_company,
                    "industry": self.get_about_industry_and_risk,
                    "risk_metrics": self.get_risk_metrics,
                    "red_flags": self.get_merchant_red_flags
                }
                
                requested_metrics = self._identify_metrics(query)
                for metric in requested_metrics:
                    if metric in metrics_map:
                        response = metrics_map[metric](merchant_id, query)
                        responses.append({metric: response})
                
                if responses:
                    return json.dumps(responses, ensure_ascii=False, cls=CustomJSONEncoder)
            
            # If no direct match, use agent for complex queries
            inputs = {"input": query}
            response = self.agent_executor.invoke(inputs)
            return str(response["output"])
            
        except Exception as e:
            error_msg = f"Error processing request: {str(e)}\n{traceback.format_exc()}"
            logging.error(error_msg)
            return f"Error processing your request: {str(e)}"

    def initialize_context(self, chat_id: str) -> str:
        base_dir = Path(__file__).resolve().parent.parent.parent.parent.parent
        data_dir = base_dir / '.ares' / 'data'
        os.makedirs(data_dir, exist_ok=True)
        context_file = data_dir / f"metrics_context_{chat_id}.json"
        if not os.path.exists(context_file):
            with open(context_file, "w") as file:
                json.dump({"history": []}, file)
        print(f"Initialized context file: {context_file}")
        return str(context_file)

    def load_existing_context(self, context_file: str, max_history: int = 5) -> List[Dict[str, str]]:
        try:
            with open(context_file, "r") as file:
                context = json.load(file)
                history = context.get("history", [])[-max_history:]
                print(f"Loaded {len(history)} history items from context")
                return history
        except FileNotFoundError:
            logger.warning(f"Context file not found: {context_file}")
            return []

    def update_context(self, context_file: str, user_message: str, llm_response: str) -> None:
        with open(context_file, "r") as file:
            context = json.load(file)
        if len(context["history"]) >= 10:
            context["history"] = context["history"][-9:]
        context["history"].append({"user": user_message, "llm": llm_response})
        with open(context_file, "w") as file:
            json.dump(context, file, indent=4)
        print(f"Updated context with new interaction")

def use_metrics_gpt(chat_id: str, user_question: str) -> str:
    try:
        print(f"MetricsGPT called with chat_id: {chat_id}, question: {user_question}")
        metrics_gpt = MetricsGPT()
        context_file = metrics_gpt.initialize_context(chat_id)
        existing_history = metrics_gpt.load_existing_context(context_file)
        for interaction in existing_history:
            metrics_gpt.memory.chat_memory.add_user_message(interaction["user"])
            metrics_gpt.memory.chat_memory.add_ai_message(interaction["llm"])
        response = metrics_gpt.run(user_question)
        if isinstance(response, str):
            metrics_gpt.update_context(context_file, user_question, response)
        else:
            metrics_gpt.update_context(context_file, user_question, str(response))
        print(f"Response generated: {response[:200]}...")  # Log first 200 chars
        return response
    except Exception as e:
        error_msg = f"Error in metrics_gpt: {str(e)}\n{traceback.format_exc()}"
        logging.error(error_msg)
        return f"Error in metrics_gpt: {str(e)}"

def main():
    print("Starting MetricsGPT CLI")
    metrics_gpt = MetricsGPT()

    while True:
        try:
            query = input("Ask about merchant metrics (or type 'exit' to quit): ")
            if query.lower() in ["exit", "quit", "q"]:
                break
            response = metrics_gpt.run(query)
            print("\nResponse:")
            print(response)
            print("\n" + "-"*50 + "\n")
        except Exception as e:
            error_msg = f"Error in main loop: {str(e)}\n{traceback.format_exc()}"
            logging.error(error_msg)
            print(f"Error: {str(e)}")
            print("\n" + "-"*50 + "\n")

if __name__ == "__main__":
    main()
