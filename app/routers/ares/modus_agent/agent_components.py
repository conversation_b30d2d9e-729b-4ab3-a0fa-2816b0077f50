from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langgraph.graph import MessagesState
from . import prompts
import json
from decimal import Decimal
from datetime import date, datetime
from pydantic import BaseModel, Field
from typing import List

from langchain.chat_models import init_chat_model

from .schema_extractor_agent import graph
from .db_agent import codegen

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return str(obj)
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        return super().default(obj)


config = {
    "configurable": {
        "thread_id": "2"
    }
}

llm = init_chat_model("openai:gpt-4.1")
llm_search = init_chat_model("perplexity:sonar-pro")

class State(MessagesState):
    is_relevant: bool
    in_context: bool
    is_greeting: str
    improved_query: str
    classification: str
    sources: List[str]
    iterations: int
    tables_and_columns: str
    merchant_id: str
    code_status: str
    code: str
    data: List
    modified_query: str
    vis_specs: dict

async def init_state(state: State):
    """
    Reset the state of the conversation.
    """
    return {
        "is_relevant": False,
        "in_context": False,
        "is_greeting": False,
        "improved_query": "",
        "classification": "",
        "sources": [],
        "iterations": 0,
        "tables_and_columns": "",
        "code_status": "",
        "code": "",
        "data": [],
        "modified_query": "",
        "viz_specs": {},
    }


# 1. Define your Pydantic Schema
class RelevanceResponse(BaseModel):
    """Information about a person."""
    is_relevant: bool = Field(
        ...,
        description="Whether the query is relevant to the context of finance and fraud investigation, detection.",
    )

async def check_relevance(state: State):
    """
    Check if the user query is relevant to the context of conversation.

    Args:
        state (State): The current state of the conversation.
    """
    response = await llm.with_structured_output(RelevanceResponse).ainvoke(
        [
            SystemMessage(
                content=prompts.RELEVANCE_SYSTEM_PROMPT
            ),
            HumanMessage(
                content=state["messages"][-1].content
            ),
        ]
    )
    return {"is_relevant": response.is_relevant}


class GreetingResponse(BaseModel):
    """Identify if the user query is a greeting."""
    is_greeting: bool= Field(
        ...,
        description="Whether the query is a greeting.",
    )

async def check_greeting(state: State):
    """
    Check if the user query is a greeting.

    Args:
        state (State): The current state of the conversation.
    """
    sytem_prompt = "You are a helpful assistant. You will be given a user query. If the query is a greeting, respond with 'True'. Otherwise, respond with 'False'. Greetings include 'hello', 'hi', 'hey', 'good morning', 'good afternoon', and 'good evening' or any other similar phrases."
    response = await llm.with_structured_output(GreetingResponse).ainvoke(
        [
            SystemMessage(
                content=sytem_prompt
            ),
            HumanMessage(
                content=state["messages"][-1].content
            ),
        ]
    )

    return {"is_greeting": response.is_greeting}


async def handle_greeting(state: State):
    """
    Handle the greeting response.

    Args:
        state (State): The current state of the conversation.
    """
    system_prompt = "You are a helpful assistant. You will be given a greetin message by user. Respond with an appropriate greeting message."
    response = await llm.ainvoke(
        [
            SystemMessage(
                content=system_prompt
            ),
            HumanMessage(
                content=state["messages"][-1].content
            ),
        ]
    )
    return {"messages": response}


async def handle_ooc_messages(state: State):
    """
    Handle out-of-context messages.

    Args:
        state (State): The current state of the conversation.
    """
    response = "Sorry, I cannot assist with this, it is out of my context and capabilities. Please ask me something different."
    return {"messages": AIMessage(content=response)}


class ContextCheckResponse(BaseModel):
    """Check if the user query is relevant to the context of conversation or a totally new query."""
    in_context: bool = Field(
        ...,
        description="Whether the query is relevant to the current context of conversation or a new query.",
    )

async def context_check(state: State):
    """
    Check if the user query is relevant to the context of conversation or a new query.

    Args:
        state (State): The current state of the conversation.
    """
    system_prompt = prompts.CONTEXT_CHECK_SYSTEM_PROMPT
    response = await llm.with_structured_output(ContextCheckResponse).ainvoke(
        [
            SystemMessage(
                content=system_prompt
            ),
            *state["messages"]
        ]
    )

    return {"in_context": response.in_context}


class ImprovedQueryResponse(BaseModel):
    """Identify if the user query is a greeting."""
    improved_query: str = Field(
        ...,
        description="The improved query based on the context of conversation.",
    )

async def improve_query(state: State) -> State:
    """
    Improve the user query based on the context of conversation.

    Args:
        state (State): The current state of the conversation.
    """
    system_prompt = prompts.IMPROVE_MESSAGE_SYSTEM_PROMPT
    response = await llm.with_structured_output(ImprovedQueryResponse).ainvoke(
        [
            SystemMessage(
                content=system_prompt
            ),
            *state["messages"],
        ]
    )
    return {"improved_query": response.improved_query}


class ClassificationResponse(BaseModel):
    """Identify the classification of the user query."""
    classification: str = Field(
        ...,
        description="The classification of the user query into different categories [LLM_QUERY, DB_QUERY, VISUALIZATION_QUERY, REPORT_QUERY, NONE]",
    )

async def classify_query(state: State) -> State:
    """
    Classify the user query into different categories [LLM_QUERY, DB_QUERY, VISUALIZATION_QUERY, REPORT_QUERY]

    Args:
        state (State): The current state of the conversation.
    """
    system_prompt = prompts.CLASSIFICATION_SYSTEM_PROMPT
    response = await llm.with_structured_output(ClassificationResponse).ainvoke(
        [
            SystemMessage(
                content=system_prompt
            ),
            HumanMessage(
                content=state["improved_query"]
            ),
        ]
    )
    return {"classification": response.classification}


class LLMQueryResponse(BaseModel):
    """Identify the classification of the user query."""
    answer: str = Field(
        ...,
        description="The answer to the user query."
        )
    sources: List[str] = Field(
        ...,
        description="The sources of the answer to the user query."
    )

async def llm_query(state: State):
    response = await llm.with_structured_output(LLMQueryResponse).ainvoke(
        [
            SystemMessage(
                content=prompts.QUESTION_ANSWER_SYSTEM_PROMPT
            ),
            HumanMessage(
                content=state["improved_query"]
            ),
        ]
    )
    return {"messages": AIMessage(content=response.answer), "sources": response.sources}


async def extract_schema(state: State):
    """
    Extract the schema from the database.

    Args:
        state (State): The current state of the conversation.
    """
    response = await graph.ainvoke(
        {"messages": [HumanMessage(content=state["improved_query"])], "iterations": 0},
        config=config
    )
    messages = response["messages"]
    tables_and_columns = messages[-1].content
    # get the last message
    return {"tables_and_columns": tables_and_columns}

async def extract_schema_vis(state: State):
    """
    Extract the schema from the database.

    Args:
        state (State): The current state of the conversation.
    """
    response = await graph.ainvoke(
        {"messages": [HumanMessage(content=state["modified_query"])], "iterations": 0},
        config=config
    )
    messages = response["messages"]
    tables_and_columns = messages[-1].content
    # get the last message
    return {"tables_and_columns": tables_and_columns}


async def handle_no_columns(state: State):
    """
    Handle the case when no columns are found.

    Args:
        state (State): The current state of the conversation.
    """
    response = "Sorry, I cannot assist with this, no columns related to the asked query exist in my knowledge. Please ask me something different."

    return {"messages": AIMessage(content=response)}


async def handle_code_success(state: State):
    """
    Handle the case when the code execution is successful.

    Args:
        state (State): The current state of the conversation.
    """
    response = "The code executed successfully. You can find the results below:\n"
    return {"messages": AIMessage(content=response)}


async def handle_code_failure(state: State):
    """
    Handle the case when the code execution fails.

    Args:
        state (State): The current state of the conversation.
    """
    response = "The code execution failed. Please check the code and try again."
    return {"messages": AIMessage(content=response)}


async def new_chat(state: State):
    """
    Start a new chat.

    Args:
        state (State): The current state of the conversation.
    """
    response = "This query is not relevant to the current context of conversation. Please start a new chat."
    return {"messages": AIMessage(content=response)}


async def db_query(state: State):
    """
    Query the database.

    Args:
        state (State): The current state of the conversation.
    """
    tables_and_columns = state["tables_and_columns"]
    merchant_id = state["merchant_id"]

    response = await codegen.ainvoke(
        {"messages": [HumanMessage(content=state["improved_query"])], "iterations": 0, "tables_and_columns": tables_and_columns, "merchant_id": merchant_id, "code": "", "data": [], "status": ""},
        config=config
    )
    if response["status"] == "success":
        return {"messages": AIMessage(content="Your query executed successfully."), "code_status": response["status"], "code": response["code"], "data": response["data"]}
    else:
        return {"messages": AIMessage(content="Your query failed to execute. Please try some other query"), "code_status": response["status"], "code": response["code"], "data": response["data"]}


class ModifiedQueryResponse(BaseModel):
    """Identify the classification of the user query."""
    modified_query: str = Field(
        ...,
        description="The modified query to fetch raw data for visualization."
        )
    
async def query_modifier(state: State):
    """
    This function will modify a query from visualization to a database query.
    """
    system_prompt = prompts.MODIFY_QUERY_SYSTEM_PROMPT
    response = await llm.with_structured_output(ModifiedQueryResponse).ainvoke(
        [
            SystemMessage(
                content=system_prompt
            ),
            HumanMessage(
                content=state["improved_query"]
            ),
        ]
    )
    return {"modified_query": response.modified_query}

async def visualization_query(state: State):
    """
    Given the extracted data from the database, this function will generate a visualization configuration.
    """
    system_message = SystemMessage(
        content=prompts.IMPROVED_GRAPH_SPEC_PROMPT
    )
    user_message = f"""
    USER QUERY: {state["improved_query"]}
    
    SAMPLE DATA: {json.dumps(state["data"][:5], ensure_ascii=False, cls=CustomJSONEncoder)}
    """
    response = await llm.ainvoke(
        [
            system_message,
            HumanMessage(
                content=user_message
            ),
        ]
    )
    # extract the json from the response by splitting on "```json" and "```"
    chart_specifications = response.content.split("```json")[1].split("```")[0].strip()
    # parse the json
    try:
        chart_specifications = json.loads(chart_specifications)
    except json.JSONDecodeError:
        chart_specifications = {}

    print("Chart Specifications:", chart_specifications)
    return {"vis_specs": chart_specifications}

async def handle_viz_end(state: State):
    """
    Handle the end of the visualization query.

    Args:
        state (State): The current state of the conversation.
    """
    if state["vis_specs"]:
        response = "The visualization query has been successfully processed. You can see the visualization below:\n"
        return {"messages": AIMessage(content=response)}
    else:
        response = "The visualization query is not clear. Please provide more details or try a different query."
        return {"messages": AIMessage(content=response)}

async def report_query(state: State):
    return {"messages": HumanMessage(content="I am a report query.")}


# Routers -------------------START----------------------
def relevance_router(state: State):
    """
    Route the relevance check based on the state.

    Args:
        state (State): The current state of the conversation.
    """
    
    if state["is_relevant"]:
        return "context_check"
    else:
        return "check_greeting"
    
def context_router(state: State):
    """
    Route the context check based on the state.

    Args:
        state (State): The current state of the conversation.
    """
    if state["in_context"]:
        return "improve_query"
    else:
        return "new_chat"
    
def greeting_router(state: State):
    """
    Route the greeting check based on the state.

    Args:
        state (State): The current state of the conversation.
    """
    if state["is_greeting"]:
        return "handle_greeting"
    else:
        return "handle_ooc_messages"
    
def classification_router(state: State):
    """
    Route the classification check based on the state.

    Args:
        state (State): The current state of the conversation.
    """
    if state["classification"] == "LLM_QUERY":
        return "llm_query"
    elif state["classification"] == "DB_QUERY":
        return "db_query"
    elif state["classification"] == "VISUALIZATION_QUERY":
        return "visualization_query"
    elif state["classification"] == "REPORT_QUERY":
        return "report_query"
    else:
        return "handle_ooc_messages"
    
def extract_schema_router(state: State):
    """
    Route the schema extraction based on the state.

    Args:
        state (State): The current state of the conversation.
    """
    tables_and_columns = json.loads(state["tables_and_columns"])
    if tables_and_columns:
        return "db_query"
    else:
        return "handle_no_columns"
    
def code_result_router(state: State):
    """
    Route the code result based on the state.

    Args:
        state (State): The current state of the conversation.
    """
    if state["code_status"] == "success":
        if state["classification"].lower() == "visualization_query":
            return "visualization_query"
        return "handle_code_success"
    else:
        return "handle_code_failure"

# Routers -------------------END----------------------

