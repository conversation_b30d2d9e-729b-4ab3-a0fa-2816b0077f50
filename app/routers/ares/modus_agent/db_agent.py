import json
import os
from langchain.chat_models import init_chat_model
from typing import List, Dict, Union
from langchain_core.messages import HumanMessage, SystemMessage, ToolMessage, AIMessage
from langchain_core.tools import tool
from langgraph.prebuilt import ToolNode, tools_condition
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field
from langgraph.graph import StateGraph, END, START
from langgraph.graph import MessagesState 
import asyncpg
from datetime import datetime
from langgraph.checkpoint.memory import MemorySaver

memory = MemorySaver()
llm = init_chat_model("openai:gpt-4.1")

class StateCodeGen(MessagesState):
    """
    Represents the state of the data analyst agent.
    Inherits from MessagesState to manage conversation history.
    """
    tables_and_columns: str = Field(
        default="",
        description="The tables and columns in the database that are needed to fullfill the request."
    )
    iterations: int = Field(
        default=0,
        description="Number of iterations for refinement."
    )
    code: str = Field(
        default="",
        description="The code generated by the agent."
    )
    data: List = Field(
        default="",
        description="Output of the code execution."
    )
    status: str = Field(
        default="",
        description="The status of the code execution."
    )
    merchant_id: str = Field(
        default="",
        description="The merchant_id to be used in the code."
    )

async def run_sql_query_async(db_uri: str, query: str, params: tuple = None):
    """
    Connects to a PostgreSQL database asynchronously using a DB URI,
    executes a given SQL query, and returns the results.

    Args:
        db_uri (str): The database connection URI (e.g., "postgresql://user:password@host:port/database").
        query (str): The SQL query string to execute.
        params (tuple, optional): A tuple of parameters to pass to the query. Defaults to None.

    Returns:
        list: A list of asyncpg.Record objects representing the query results.
              Returns an empty list if no results are returned (e.g., for INSERT/UPDATE/DELETE).
    """
    conn = None  # Initialize conn to None
    try:
        # Establish an asynchronous connection to the database
        conn = await asyncpg.connect(db_uri)
        print(f"Connected to PostgreSQL database using URI: {db_uri}")

        if params:
            # Execute the query with parameters
            results = await conn.fetch(query, *params)
        else:
            # Execute the query without parameters
            results = await conn.fetch(query)

        # convert results to a json-serializable format
        results = [dict(record) for record in results]
        print(f"Query executed successfully: '{query}'")
        return {
            "status": "success",
            "message": "Query executed successfully.",
            "results": results
        }

    except asyncpg.exceptions.PostgresError as e:
        print(f"PostgreSQL error: {e}")
        return {
            "status": "error",
            "message": str(e),
            "results": []
        }
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return {
            "status": "error",
            "message": str(e),
            "results": []
        }
    finally:
        if conn:
            # Close the connection
            await conn.close()
            print("Database connection closed.")

async def sql_query_executer(state: StateCodeGen):
    """
    Executes the generated SQL query and returns the success or failure message with error details if any.
    
    Args:
        query (str): The SQL query to be executed.

    Returns:
        str: A message indicating the success or failure of the query execution.
    """

    DATABASE_URL = "postgresql://postgres:<EMAIL>:5432/postgres"

    query = state["code"]

    # TODO: PRE PROCESSING OF THE QUERY TO SEE IF IT IS VALID OR NOT
    if "```" in query:
        query = query.split("```")[1]
        query = query.split("sql\n")[1]
        query = query.strip()
    else:
        return {"messages": AIMessage(content="The query is not in the correct format (```sql\n <QUERY> ``` not found). Please provide a valid SQL query."), "data": [], "status": "failed"}
    # Execute the SQL query asynchronously
    results = await run_sql_query_async(DATABASE_URL, query)
    if results and results.get("status") == "success":
        # If the query was successful, return the results
        return {"messages": AIMessage(content=results.get("message")), "data": results.get("results"), "status": "success", "code": query}
    else:
        # If there was an error, return the error message
        return {"messages": AIMessage(content=f"The following error is encountered while running the query: \n\n{results.get("message")}"), "data": [], "status": "failed", "code": query}
    

async def codegenAgent(state: StateCodeGen):
    """This function takes a user query and generates a SQL query using the LLM and then executes it.
    It also handles the iteration process to refine the SQL query if needed.
    """
    tables_and_columns = state["tables_and_columns"]
    iterations = state["iterations"]
    merchant_id = state["merchant_id"]

    system_prompt = f"""
    You are a highly skilled data analyst agent specializing in SQL query generation for a PostgreSQL database. Your primary goal is to construct accurate and efficient SQL queries based on user requests, a specific merchant_id, and the provided database schema.

    **Key Instructions:**

    1.  **Understand the Goal:** Carefully analyze the user's query to understand their information needs.
    2.  **Schema Adherence:** Strictly use the tables and columns provided in the 'DATABASE SCHEMA' section. Pay close attention to column datatypes and sample values to ensure correct query construction.
    3.  **Merchant-Specific Queries:**
        * The `merchant_id` ('{merchant_id}') is a **critical filter** and is present as a column in **all relevant tables**.
        * **ALWAYS** include a `WHERE` clause in your SQL queries to filter results for this specific `merchant_id` unless explicitly told otherwise for a very specific analytical reason (which is rare). For example: `WHERE merchant_id = '{merchant_id}'`.
    4.  **Table Selection:** If multiple tables are provided, choose only the necessary tables to fulfill the user's request. Be mindful of how tables might join if information from multiple tables is needed. If join conditions are not obvious from the column names, you may need to infer them or state if ambiguity exists.
    5.  **Query Generation:** Construct a single, executable SQL query.
    6.  **Result Limitation:** To prevent system overload, **ALL data-retrieving SELECT queries MUST include `LIMIT 200` at the end of the query.** This ensures that no more than 200 rows are fetched. For example, `SELECT * FROM your_table WHERE condition LIMIT 200;`. This is a strict requirement.
    7.  **Error Handling & Refinement:** If an initial query fails, you will receive an error message. Analyze this error carefully to identify the issue (e.g., incorrect table/column names, syntax errors, datatype mismatches, incorrect join conditions, missing LIMIT clause). Revise the SQL query to correct the error. You will perform iterations to achieve a successful query.
    8.  **Date Awareness:** The current date is {datetime.today().strftime('%Y-%m-%d')}. Use this if the user's query involves relative date calculations (e.g., "today," "last month"). 
    9. **Keeping Time related columns:** Try to keep the time related columns in the results whenever possible to give user a better context of the data. Like if the user askes for some financial data for past x years, try to keep the date column in the results so that user can see the trend of the data over the years.

    **DATABASE SCHEMA:**
    {tables_and_columns}

    **User's merchant_id for filtering:** {merchant_id}

    Follow these steps precisely:
    1.  Based on the user query, the DATABASE SCHEMA, the merchant_id ({merchant_id}), and the mandatory LIMIT 200 clause, generate the SQL query.
    2.  Ensure the query is formatted correctly within ```sql\n<QUERY>\n```.
    """

    if iterations == 0:
        # First iteration
        user_query = state["messages"][-1].content
        system_message = SystemMessage(content=system_prompt)
        human_message = HumanMessage(content=user_query)
        messages = [system_message, human_message]
        response = await llm.ainvoke(messages)
        return {"messages": response, "iterations": iterations + 1, "code": response.content}
    else:
        messages = state["messages"]
        response = await llm.ainvoke(messages)
        return {"messages": response, "iterations": iterations + 1, "code": response.content}


async def router(state: StateCodeGen):
    """
    This function routes the state to the appropriate function based on the current state.
    It handles the iteration process and executes the SQL query if needed.
    """
    iterations = state["iterations"]

    if state["status"] == "success" or iterations >= 3:
        # If the query executed successfully, return the results
        return "stop"
    elif state["status"] == "failed":
        return "codegenAgent"
    
async def stop(state: StateCodeGen):
    """
    This function stops the state graph and returns the final results.
    """
    if state["status"] == "success":
        return {"messages": AIMessage(content="success"), "data": state["data"], "status": "success"}
    else:
        return {"messages": AIMessage(content="failed"), "data": [], "status": "failed"}
    
graph_builder = StateGraph(StateCodeGen)
graph_builder.add_node("codegenAgent", codegenAgent)
graph_builder.add_node("sql_query_executer", sql_query_executer)
graph_builder.add_node("stop", stop)

graph_builder.add_edge("codegenAgent", "sql_query_executer")
graph_builder.add_conditional_edges("sql_query_executer", router, {
    "codegenAgent": "codegenAgent",
    "stop": "stop"
})
graph_builder.set_entry_point("codegenAgent")

codegen = graph_builder.compile(checkpointer=memory)