import json
import os
import re
import requests
from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, Path
from sqlalchemy.orm import Session
from uuid import UUID
from ..models import models
from datetime import datetime, timedelta

from .apiProtection import get_current_user

from ..database import get_db
from ..utils.llm import create_and_run_perp_pipeline
from ..static.parsers_cr import about_the_company_parser, about_the_industry_parser
from ..static.prompts_cr import PROMPT_INFORMATION_ABOUT_COMPANY, PROMPT_INFORMATION_ABOUT_INDUSTY
from .creditDashboardInternal import (
    get_financials_table_internal,
    get_about_company_internal,
    get_about_industry_internal,
    get_company_metrics_internal,
    get_external_data_internal,
    get_flags_from_auditor_disclosures_internal,
    get_annual_report_insights_internal,
    get_risk_metrics_internal
)

def format_indian_currency(value):
    if value is None:
        return None
    value = float(value)
    if value < 0:
        value = abs(value)
        prefix = "-₹"
    else:
        prefix = "₹"
    
    # Convert to string and split by decimal
    str_value = f"{value:,.0f}"
    parts = str_value.split(".")
    num = parts[0]
    
    # Convert to Indian format
    last_three = num[-3:]
    other_numbers = num[:-3]
    if other_numbers:
        formatted = other_numbers[::-1].replace(",", "")  # Remove existing commas
        formatted = ",".join(formatted[i:i+2] for i in range(0, len(formatted), 2))
        formatted = formatted[::-1] + "," + last_three
    else:
        formatted = last_three
    
    return prefix + formatted

router = APIRouter()

# Api's for fetching financial data
@router.get("/{merchant_id}/financialsTable")
async def get_financials_table(
    merchant_id: UUID = Path(..., title="Merchant ID"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
):
    return get_financials_table_internal(merchant_id, db)

@router.get("/{merchant_id}/aboutTheCompany")
async def get_about_the_company(
    merchant_id: UUID = Path(..., title="Merchant ID"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
):
    return get_about_company_internal(merchant_id, db)

@router.get("/{merchant_id}/aboutIndustryAndRisk")
async def get_about_industry(
    merchant_id: UUID = Path(..., title="Merchant ID"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
):
    return get_about_industry_internal(merchant_id, db)

@router.get("/{merchant_id}/getCompanyMetrics")
async def get_company_metrics(
    merchant_id: UUID = Path(..., title="Merchant ID"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
):
    return get_company_metrics_internal(merchant_id, db)

@router.get("/downloadALldata")
async def download_all_data(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
):
    """
    Download all data for a specific merchant
    """
    try:
        # get all merchants
        merchants = db.query(models.probe_merchant).all()
        if not merchants:
            raise HTTPException(status_code=404, detail="No merchants found")
        
        # get all metrics
        metrics = db.query(models.MetricStore).all()
        if not metrics:
            raise HTTPException(status_code=404, detail="No metrics found")
        
        # get merchant columns 
        all_merchant_fields = list(models.probe_merchant.__table__.columns.keys())
        all_metric_codes = list(set([metric.metric_code for metric in metrics]))
        
        final_data = {}
        for merchant_field in all_merchant_fields:
            final_data[merchant_field] = []
        for metric_code in all_metric_codes:
            final_data[metric_code] = []

        final_data["Revenue"] = []

        for merchant in merchants:
            for merchant_field in all_merchant_fields:
                final_data[merchant_field].append(getattr(merchant, merchant_field))
            for metric_code in all_metric_codes:
                metric = db.query(models.merchant_metrics).filter(models.merchant_metrics.merchant_id == merchant.merchant_id, models.merchant_metrics.metric_type == metric_code).order_by(models.merchant_metrics.year.desc()).first()
                if not metric:
                    final_data[metric_code].append(None)
                else:
                    final_data[metric_code].append(metric.metric_value)
            
            financials = db.query(models.probe_financials).filter(models.probe_financials.merchant_id == merchant.merchant_id, models.probe_financials.nature == "STANDALONE").order_by(models.probe_financials.year.desc()).first()
            if not financials:
                final_data["Revenue"].append(None)
            else:
                final_data["Revenue"].append(financials.net_revenue)

        # save it as csv
        import pandas as pd
        df = pd.DataFrame(final_data)
        df.to_csv("all_data.csv", index=False)
        return {
            "success": True,
            "message": "All data downloaded successfully",
            "data": final_data
        }
    except Exception as e:
        print("Error in download_all_data:", str(e))
        return {
            "success": False,
            "message": str(e),
            "data": None
        }

@router.get("/{merchant_id}/getExternalData")
async def get_external_data(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    return get_external_data_internal(merchant_id, db)

@router.get("/{merchant_id}/getFlagsFromAuditorDisclosures")
async def get_flags_from_auditor_disclosures(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    return get_flags_from_auditor_disclosures_internal(merchant_id, db)

@router.get("/{merchant_id}/getAnnualReportInsights")
async def get_annual_report_insights(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    return get_annual_report_insights_internal(merchant_id, db)

@router.get("/{merchant_id}/getRiskMetrics")
async def get_risk_metrics(
    merchant_id: UUID = Path(..., title="Merchant ID"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
):
    return get_risk_metrics_internal(merchant_id, db)

#  to create an api that will populate red flags data in the database