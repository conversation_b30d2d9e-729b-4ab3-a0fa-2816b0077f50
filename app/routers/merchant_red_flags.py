from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
from ..database import get_db
from ..models.models import merchant_red_flags, rules_store, merchant_metrics, Merchant
from pydantic import BaseModel
import uuid
import logging
import pandas as pd
from sqlalchemy import select, join

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter()

class MerchantRedFlagCreate(BaseModel):
    merchant_id: uuid.UUID
    rule_code: str
    description: Optional[str] = None
    severity: Optional[str] = None
    metric_values: Optional[dict] = None
    metric_data_timestamp: Optional[datetime] = None
    notes: Optional[str] = None

class MerchantRedFlagResponse(MerchantRedFlagCreate):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    rule_name: Optional[str] = None
    rule_description: Optional[str] = None
    rule_type: Optional[str] = None
    rule_severity: Optional[str] = None
    rule_fraud_type: Optional[str] = None
    rule: Optional[dict] = None

    class Config:
        from_attributes = True

def validate_rule_code(db: Session, rule_code: str) -> bool:
    rule = db.query(rules_store).filter(rules_store.code == rule_code).first()
    if not rule:
        logger.warning(f"Rule code {rule_code} not found in rules_store")
    return rule is not None

def create_red_flag(db: Session, red_flag_data: MerchantRedFlagCreate) -> merchant_red_flags:
    validate_rule_code(db, red_flag_data.rule_code)  # Just log warning if rule not found
    
    db_red_flag = merchant_red_flags(
        merchant_id=red_flag_data.merchant_id,
        rule_code=red_flag_data.rule_code,
        description=red_flag_data.description,
        severity=red_flag_data.severity,
        metric_values=red_flag_data.metric_values,
        metric_data_timestamp=red_flag_data.metric_data_timestamp,
        notes=red_flag_data.notes
    )
    db.add(db_red_flag)
    db.commit()
    db.refresh(db_red_flag)
    return db_red_flag

@router.post("/", response_model=MerchantRedFlagResponse)
def create_merchant_red_flag(red_flag: MerchantRedFlagCreate, db: Session = Depends(get_db)):
    try:
        return create_red_flag(db, red_flag)
    except Exception as e:
        logger.error(f"Error creating red flag: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

def get_merchant_red_flags_internal(merchant_id: uuid.UUID, db: Session, rule_type: str = None) -> List[dict]:
    """Internal function to get merchant red flags that can be used by other modules."""
    query = db.query(
        merchant_red_flags,
        rules_store.name.label('rule_name'),
        rules_store.description.label('rule_description'),
        rules_store.type.label('rule_type'),
        rules_store.severity.label('rule_severity'),
        rules_store.fraud_type.label('rule_fraud_type'),
        rules_store.rule.label('rule')
    ).outerjoin(
        rules_store,
        merchant_red_flags.rule_code == rules_store.code
    ).filter(
        merchant_red_flags.merchant_id == merchant_id
    )
    
    if rule_type:
        query = query.filter(
            (merchant_red_flags.category == rule_type) | (rules_store.type == rule_type)
        )
    
    red_flags = query.all()
    
    if not red_flags:
        return []
        
    response = []
    for flag, rule_name, rule_desc, rule_type, rule_sev, fraud_type, rule in red_flags:
        response.append({
            "id": flag.id,
            "merchant_id": flag.merchant_id,
            "rule_code": flag.rule_code,
            "description": flag.description,
            "severity": flag.severity,
            "metric_values": flag.metric_values,
            "created_at": flag.created_at,
            "updated_at": flag.updated_at,
            "metric_data_timestamp": flag.metric_data_timestamp,
            "notes": flag.notes,
            "rule_name": rule_name,
            "rule_description": rule_desc,
            "rule_type": flag.category if hasattr(flag, 'category') and flag.category is not None else rule_type,
            "rule_severity": rule_sev,
            "rule_fraud_type": fraud_type,
            "rule": rule
        })
    
    return response

@router.get("/merchant/{merchant_id}", response_model=List[MerchantRedFlagResponse])
def get_merchant_red_flags(
    merchant_id: uuid.UUID, 
    rule_type: Optional[str] = Query(None, description="Filter by rule type"), 
    db: Session = Depends(get_db)
):
    return get_merchant_red_flags_internal(merchant_id, db, rule_type)

@router.get("/rule/{rule_code}", response_model=List[MerchantRedFlagResponse])
def get_red_flags_by_rule(rule_code: str, db: Session = Depends(get_db)):
    validate_rule_code(db, rule_code)  # Just log warning if rule not found
    
    red_flags = db.query(merchant_red_flags).filter(merchant_red_flags.rule_code == rule_code).all()
    if not red_flags:
        return []
    return red_flags

@router.put("/{red_flag_id}", response_model=MerchantRedFlagResponse)
def update_red_flag(red_flag_id: uuid.UUID, red_flag: MerchantRedFlagCreate, db: Session = Depends(get_db)):
    validate_rule_code(db, red_flag.rule_code)  # Just log warning if rule not found
    
    db_red_flag = db.query(merchant_red_flags).filter(merchant_red_flags.id == red_flag_id).first()
    if not db_red_flag:
        raise HTTPException(status_code=404, detail="Red flag not found")
    
    for key, value in red_flag.dict().items():
        setattr(db_red_flag, key, value)
    
    db.commit()
    db.refresh(db_red_flag)
    return db_red_flag

@router.delete("/{red_flag_id}")
def delete_red_flag(red_flag_id: uuid.UUID, db: Session = Depends(get_db)):
    db_red_flag = db.query(merchant_red_flags).filter(merchant_red_flags.id == red_flag_id).first()
    if not db_red_flag:
        raise HTTPException(status_code=404, detail="Red flag not found")
    
    db.delete(db_red_flag)
    db.commit()
    return {"message": "Red flag deleted successfully"}

def get_merchant_metrics_wide_format(db: Session) -> pd.DataFrame:
    # Query all metrics with merchant info
    query = select(
        merchant_metrics,
        Merchant.legal_name
    ).join(
        Merchant,
        merchant_metrics.merchant_id == Merchant.id
    )
    results = db.execute(query).all()
    
    # Convert to list of dictionaries
    metrics_list = []
    for metric, legal_name in results:
        metrics_list.append({
            'merchant_id': str(metric.merchant_id),
            'legal_name': legal_name,
            'metric_name': metric.metric_type,
            'metric_value': metric.metric_value,
            'year': metric.year
        })
    
    if not metrics_list:
        return pd.DataFrame(columns=['merchant_id', 'legal_name'])
    
    # Convert to DataFrame
    df = pd.DataFrame(metrics_list)
    
    # Create column names as metric_name_year
    df['column_name'] = df['metric_name'] + '_' + df['year'].astype(str)
    
    # Handle duplicates by taking the latest value
    df = df.sort_values('year').drop_duplicates(['merchant_id', 'column_name'], keep='last')
    
    # Pivot the data
    wide_df = df.pivot(
        index=['merchant_id', 'legal_name'],
        columns='column_name',
        values='metric_value'
    ).reset_index()
    
    return wide_df

@router.get("/merchants/metrics/csv")
def get_all_merchant_metrics_csv(db: Session = Depends(get_db)):
    try:
        # Get wide format data for all merchants
        df = get_merchant_metrics_wide_format(db)
        
        # Save to CSV
        csv_path = "all_merchants_metrics.csv"
        df.to_csv(csv_path, index=False)
        
        return {"message": f"CSV file created at {csv_path}"}
    except Exception as e:
        logger.error(f"Error creating metrics CSV: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 