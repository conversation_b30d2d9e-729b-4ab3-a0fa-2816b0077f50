from fastapi import APIRouter, Depends, HTTPException, Query, Body
from typing import List, Dict, Any, Optional
from app.models.models import TransactionData
from sqlalchemy.inspection import inspect
from sqlalchemy.orm import Session
from app.database import get_db
from .apiProtection import get_current_user
import app.models.models as models
from datetime import datetime
from sqlalchemy import func, and_, or_

router = APIRouter()

# Default field mapping for UI (based on image and model fields)
DEFAULT_FIELD_MAPPING_TRANSACTIONS = {
    "Row Unique Id": "txn_id",
    "Datetime": "txn_datetime",
    "Fraud Label": "fraud_label",
    "Amount": "txn_amt",
    "Application ID": "",
    "Account ID": "mer_id",
    "Transaction ID": "txn_id"
}

@router.get("/config", response_model=Dict[str, Any])
def get_backtesting_config(current_user: models.User = Depends(get_current_user)):
    # Only one datatable for now
    datatable_name = "transaction_data_table"
    # Get all columns and types from SQLAlchemy model
    mapper = inspect(TransactionData)
    columns = {}
    field_mappings = {}
    for column in mapper.columns:
        columns[column.name] = str(column.type)
    # Prepare default field mappings info
    default_field_mappings_info = {}
    for label, field in DEFAULT_FIELD_MAPPING_TRANSACTIONS.items():
        default_field_mappings_info[field] = {
            "label": label,
            "matching_fields": field.split(",")
        }
    response = {
        "data_tables": [
            {
                "table_details" : {
                    "id": datatable_name,
                    "name": "Transactions",
                    "description": "Transaction level data with fraud labels",
                    "schema": columns
                },
                "default_field_mapping": default_field_mappings_info,
            }
        ]
    }
    return response

@router.get("/values/{data_table}/{field_name}")
def get_distinct_field_values(
    data_table: str,
    field_name: str,
    response_type: str = Query("all", enum=["all", "min_max"]),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    # Only support transaction_data_table for now
    if data_table != "transaction_data_table":
        raise HTTPException(status_code=400, detail="Unsupported data table")
    model = TransactionData
    mapper = inspect(model)
    if field_name not in mapper.columns:
        raise HTTPException(status_code=400, detail="Invalid field name")
    
    column = mapper.columns[field_name]
    is_datetime = str(column.type).lower().startswith('datetime')
    is_numeric = str(column.type).lower() in ['numeric', 'integer', 'float', 'decimal']
    
    if response_type == "min_max" and is_datetime:
        min_val = db.query(getattr(model, field_name)).order_by(getattr(model, field_name).asc()).first()[0]
        max_val = db.query(getattr(model, field_name)).order_by(getattr(model, field_name).desc()).first()[0]
        return {"field": field_name, "min_value": min_val, "max_value": max_val}
    
    results = db.query(getattr(model, field_name)).distinct().all()
    values = [row[0] for row in results]
    return {"field": field_name, "distinct_values": values}

@router.post("/bucket-counts/{data_table}")
def get_bucket_counts(
    data_table: str,
    bucket_request: Dict[str, Any] = Body(...),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    if data_table != "transaction_data_table":
        raise HTTPException(status_code=400, detail="Unsupported data table")
    
    model = TransactionData
    mapper = inspect(model)
    field_name = bucket_request.get("field")
    
    if field_name not in mapper.columns:
        raise HTTPException(status_code=400, detail="Invalid field name")
    
    column = mapper.columns[field_name]
    is_datetime = str(column.type).lower().startswith('datetime')
    is_numeric = str(column.type).lower() in ['numeric', 'integer', 'float', 'decimal']
    total_count = db.query(func.count()).select_from(model).scalar()
    
    buckets = bucket_request.get("buckets", [])
    result_buckets = []
    
    for bucket in buckets:
        bucket_type = bucket.get("type")
        count = 0
        
        if is_datetime:
            if bucket_type == "auto":
                date_range = bucket.get("dateRange", {})
                start_date = datetime.fromisoformat(date_range.get("start"))
                end_date = datetime.fromisoformat(date_range.get("end"))
                count = db.query(func.count()).filter(
                    and_(
                        getattr(model, field_name) >= start_date,
                        getattr(model, field_name) < end_date
                    )
                ).scalar()
            elif bucket_type == "custom":
                date_range = bucket.get("dateRange", {})
                start_date = datetime.fromisoformat(date_range.get("start"))
                end_date = datetime.fromisoformat(date_range.get("end"))
                count = db.query(func.count()).filter(
                    and_(
                        getattr(model, field_name) >= start_date,
                        getattr(model, field_name) <= end_date
                    )
                ).scalar()
        elif is_numeric:
            range_info = bucket.get("range", {})
            min_val = range_info.get("min")
            max_val = range_info.get("max")
            if min_val is not None and max_val is not None:
                # Debug: Check actual values in DB
                sample_values = db.query(getattr(model, field_name)).limit(5).all()
                print(f"Sample values for {field_name}: {sample_values}")
                print(f"Querying range: {min_val} to {max_val}")
                
                count = db.query(func.count()).filter(
                    and_(
                        getattr(model, field_name) >= min_val,
                        getattr(model, field_name) <= max_val
                    )
                ).scalar()
                print(f"Count for range {min_val}-{max_val}: {count}")
        else:
            # For string fields
            values = bucket.get("values", [])
            if values:
                count = db.query(func.count()).filter(
                    getattr(model, field_name).in_(values)
                ).scalar()
        
        percentage = (count / total_count * 100) if total_count > 0 else 0
        bucket["count"] = count
        bucket["percentage"] = round(percentage, 2)
        result_buckets.append(bucket)
    
    return {"field": field_name, "buckets": result_buckets} 