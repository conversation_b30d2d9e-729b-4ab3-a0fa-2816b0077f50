from fastapi import APIRouter, Depends, HTTPException, Query, Body
from typing import List, Dict, Any, Optional
from app.models.models import TransactionData, BacktestingAnalysis
from sqlalchemy.inspection import inspect
from sqlalchemy.orm import Session
from app.database import get_db
from .apiProtection import get_current_user
import app.models.models as models
from datetime import datetime
from sqlalchemy import func, and_, or_
from app.schemas.request_models import BacktestingAnalysisRequest, BacktestingAnalysisResponse
from app.utils.backtesting_sql_generator import generate_combined_sql
from app.routers.dashBoardApis import run_sql_query_async
import os
import json

router = APIRouter()

# Default field mapping for UI (based on image and model fields)
DEFAULT_FIELD_MAPPING_TRANSACTIONS = {
    "Row Unique Id": "txn_id",
    "Datetime": "txn_datetime",
    "Fraud Label": "fraud_label",
    "Amount": "txn_amt",
    "Application ID": "",
    "Account ID": "mer_id",
    "Transaction ID": "txn_id"
}

@router.get("/config", response_model=Dict[str, Any])
def get_backtesting_config(current_user: models.User = Depends(get_current_user)):
    # Only one datatable for now
    datatable_name = "transaction_data_table"
    # Get all columns and types from SQLAlchemy model
    mapper = inspect(TransactionData)
    columns = {}
    field_mappings = {}
    for column in mapper.columns:
        columns[column.name] = str(column.type)
    # Prepare default field mappings info
    default_field_mappings_info = {}
    for label, field in DEFAULT_FIELD_MAPPING_TRANSACTIONS.items():
        default_field_mappings_info[field] = {
            "label": label,
            "matching_fields": field.split(",")
        }
    response = {
        "data_tables": [
            {
                "table_details" : {
                    "id": datatable_name,
                    "name": "Transactions",
                    "description": "Transaction level data with fraud labels",
                    "schema": columns
                },
                "default_field_mapping": default_field_mappings_info,
            }
        ]
    }
    return response

@router.get("/values/{data_table}/{field_name}")
def get_distinct_field_values(
    data_table: str,
    field_name: str,
    response_type: str = Query("all", enum=["all", "min_max"]),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    # Only support transaction_data_table for now
    if data_table != "transaction_data_table":
        raise HTTPException(status_code=400, detail="Unsupported data table")
    model = TransactionData
    mapper = inspect(model)
    if field_name not in mapper.columns:
        raise HTTPException(status_code=400, detail="Invalid field name")

    column = mapper.columns[field_name]
    is_datetime = str(column.type).lower().startswith('datetime')
    is_numeric = str(column.type).lower() in ['numeric', 'integer', 'float', 'decimal']

    if response_type == "min_max" and is_datetime:
        min_val = db.query(getattr(model, field_name)).order_by(getattr(model, field_name).asc()).first()[0]
        max_val = db.query(getattr(model, field_name)).order_by(getattr(model, field_name).desc()).first()[0]
        return {"field": field_name, "min_value": min_val, "max_value": max_val}

    results = db.query(getattr(model, field_name)).distinct().all()
    values = [row[0] for row in results]
    return {"field": field_name, "distinct_values": values}

@router.post("/bucket-counts/{data_table}")
def get_bucket_counts(
    data_table: str,
    bucket_request: Dict[str, Any] = Body(...),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    if data_table != "transaction_data_table":
        raise HTTPException(status_code=400, detail="Unsupported data table")

    model = TransactionData
    mapper = inspect(model)
    field_name = bucket_request.get("field")

    if field_name not in mapper.columns:
        raise HTTPException(status_code=400, detail="Invalid field name")

    column = mapper.columns[field_name]
    print(f"Column type: {column.type}")
    is_datetime = str(column.type).lower().startswith('datetime')
    is_numeric = any(t in str(column.type).lower() for t in ['numeric', 'integer', 'float', 'decimal', 'decimal('])
    print(f"Is numeric: {is_numeric}")
    total_count = db.query(func.count()).select_from(model).scalar()

    buckets = bucket_request.get("buckets", [])
    result_buckets = []

    for bucket in buckets:
        bucket_type = bucket.get("type")
        count = 0

        if is_datetime:
            if bucket_type == "auto":
                date_range = bucket.get("dateRange", {})
                start_date = datetime.fromisoformat(date_range.get("start"))
                end_date = datetime.fromisoformat(date_range.get("end"))
                count = db.query(func.count()).filter(
                    and_(
                        getattr(model, field_name) >= start_date,
                        getattr(model, field_name) < end_date
                    )
                ).scalar()
            elif bucket_type == "custom":
                date_range = bucket.get("dateRange", {})
                start_date = datetime.fromisoformat(date_range.get("start"))
                end_date = datetime.fromisoformat(date_range.get("end"))
                count = db.query(func.count()).filter(
                    and_(
                        getattr(model, field_name) >= start_date,
                        getattr(model, field_name) <= end_date
                    )
                ).scalar()
        elif is_numeric:
            range_info = bucket.get("range", {})
            min_val = range_info.get("min")
            max_val = range_info.get("max")
            if min_val is not None and max_val is not None:
                # Debug: Check actual values in DB
                sample_values = db.query(getattr(model, field_name)).limit(5).all()
                print(f"Sample values for {field_name}: {sample_values}")
                print(f"Querying range: {min_val} to {max_val}")

                count = db.query(func.count()).filter(
                    and_(
                        getattr(model, field_name) >= min_val,
                        getattr(model, field_name) <= max_val
                    )
                ).scalar()
                print(f"Count for range {min_val}-{max_val}: {count}")
        else:
            # For string fields
            values = bucket.get("values", [])
            if values:
                count = db.query(func.count()).filter(
                    getattr(model, field_name).in_(values)
                ).scalar()

        percentage = (count / total_count * 100) if total_count > 0 else 0
        bucket["count"] = count
        bucket["percentage"] = round(percentage, 2)
        result_buckets.append(bucket)

    return {"field": field_name, "buckets": result_buckets}


@router.post("/rule-analysis", response_model=BacktestingAnalysisResponse)
async def create_rule_analysis(
    request: BacktestingAnalysisRequest,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Create a new rule analysis configuration and generate SQL queries.
    """
    try:
        # Validate analysis type
        if request.analysisType != "rule":
            raise HTTPException(status_code=400, detail="Analysis type must be 'rule' for this endpoint")

        # Check if analysis_id already exists
        existing = db.query(BacktestingAnalysis).filter(
            BacktestingAnalysis.analysis_id == request.analysisId
        ).first()

        if existing:
            raise HTTPException(status_code=400, detail="Analysis ID already exists")

        # Convert request to dict for processing
        config_dict = request.model_dump()

        # Generate SQL queries
        sql_queries = generate_combined_sql(config_dict)

        # Create new analysis record
        analysis = BacktestingAnalysis(
            analysis_id=request.analysisId,
            analysis_type=request.analysisType,
            analysis_config=config_dict,
            prelim_sql=sql_queries["preliminary_sql"],
            pivot_sql=sql_queries["combined_sql"],
            status="created"
        )

        db.add(analysis)
        db.commit()
        db.refresh(analysis)

        return BacktestingAnalysisResponse(
            id=str(analysis.id),
            analysis_id=analysis.analysis_id,
            analysis_type=analysis.analysis_type,
            status=analysis.status,
            preliminary_sql=analysis.prelim_sql,
            pivot_sql=analysis.pivot_sql,
            created_at=analysis.created_at,
            updated_at=analysis.updated_at
        )

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error creating rule analysis: {str(e)}")


@router.post("/metric-analysis", response_model=BacktestingAnalysisResponse)
async def create_metric_analysis(
    request: BacktestingAnalysisRequest,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Create a new metric analysis configuration and generate SQL queries.
    """
    try:
        # Validate analysis type
        if request.analysisType != "metric":
            raise HTTPException(status_code=400, detail="Analysis type must be 'metric' for this endpoint")

        # Check if analysis_id already exists
        existing = db.query(BacktestingAnalysis).filter(
            BacktestingAnalysis.analysis_id == request.analysisId
        ).first()

        if existing:
            raise HTTPException(status_code=400, detail="Analysis ID already exists")

        # Convert request to dict for processing
        config_dict = request.model_dump()

        # Generate SQL queries
        sql_queries = generate_combined_sql(config_dict)

        # Create new analysis record
        analysis = BacktestingAnalysis(
            analysis_id=request.analysisId,
            analysis_type=request.analysisType,
            analysis_config=config_dict,
            prelim_sql=sql_queries["preliminary_sql"],
            pivot_sql=sql_queries["combined_sql"],
            status="created"
        )

        db.add(analysis)
        db.commit()
        db.refresh(analysis)

        return BacktestingAnalysisResponse(
            id=str(analysis.id),
            analysis_id=analysis.analysis_id,
            analysis_type=analysis.analysis_type,
            status=analysis.status,
            preliminary_sql=analysis.prelim_sql,
            pivot_sql=analysis.pivot_sql,
            created_at=analysis.created_at,
            updated_at=analysis.updated_at
        )

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error creating metric analysis: {str(e)}")


@router.get("/analysis/{analysis_id}", response_model=BacktestingAnalysisResponse)
async def get_analysis(
    analysis_id: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get analysis configuration and SQL queries by analysis ID.
    """
    analysis = db.query(BacktestingAnalysis).filter(
        BacktestingAnalysis.analysis_id == analysis_id
    ).first()

    if not analysis:
        raise HTTPException(status_code=404, detail="Analysis not found")

    return BacktestingAnalysisResponse(
        id=str(analysis.id),
        analysis_id=analysis.analysis_id,
        analysis_type=analysis.analysis_type,
        status=analysis.status,
        preliminary_sql=analysis.prelim_sql,
        pivot_sql=analysis.pivot_sql,
        created_at=analysis.created_at,
        updated_at=analysis.updated_at
    )


@router.post("/analysis/{analysis_id}/execute")
async def execute_analysis(
    analysis_id: str,
    query_type: str = Query("combined", enum=["preliminary", "pivot", "combined"]),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Execute the SQL queries for a specific analysis and return results.
    """
    try:
        # Get analysis from database
        analysis = db.query(BacktestingAnalysis).filter(
            BacktestingAnalysis.analysis_id == analysis_id
        ).first()

        if not analysis:
            raise HTTPException(status_code=404, detail="Analysis not found")

        # Determine which SQL to execute
        if query_type == "preliminary":
            sql_to_execute = analysis.prelim_sql
        elif query_type == "pivot":
            sql_to_execute = analysis.pivot_sql
        else:  # combined
            sql_to_execute = analysis.pivot_sql

        if not sql_to_execute:
            raise HTTPException(status_code=400, detail="No SQL query available for this analysis")

        # Update status to processing
        analysis.status = "processing"
        db.commit()

        # Execute SQL query
        DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:<EMAIL>:5432/postgres")
        if DATABASE_URL.startswith("postgres://"):
            DATABASE_URL = DATABASE_URL.replace("postgres://", "postgresql://", 1)

        result = await run_sql_query_async(DATABASE_URL, sql_to_execute)

        if result["status"] == "error":
            # Update status to failed
            analysis.status = "failed"
            db.commit()
            raise HTTPException(status_code=500, detail=f"SQL execution error: {result['message']}")

        # Update status to completed
        analysis.status = "completed"
        db.commit()

        return {
            "analysis_id": analysis_id,
            "query_type": query_type,
            "status": "success",
            "data": result["results"],
            "row_count": len(result["results"])
        }

    except HTTPException:
        raise
    except Exception as e:
        # Update status to failed
        if 'analysis' in locals():
            analysis.status = "failed"
            db.commit()
        raise HTTPException(status_code=500, detail=f"Error executing analysis: {str(e)}")


@router.get("/analysis", response_model=List[BacktestingAnalysisResponse])
async def list_analyses(
    analysis_type: Optional[str] = Query(None, enum=["rule", "metric"]),
    status: Optional[str] = Query(None, enum=["created", "processing", "completed", "failed"]),
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    List all analyses with optional filtering.
    """
    query = db.query(BacktestingAnalysis)

    if analysis_type:
        query = query.filter(BacktestingAnalysis.analysis_type == analysis_type)

    if status:
        query = query.filter(BacktestingAnalysis.status == status)

    analyses = query.order_by(BacktestingAnalysis.created_at.desc()).offset(offset).limit(limit).all()

    return [
        BacktestingAnalysisResponse(
            id=str(analysis.id),
            analysis_id=analysis.analysis_id,
            analysis_type=analysis.analysis_type,
            status=analysis.status,
            preliminary_sql=analysis.prelim_sql,
            pivot_sql=analysis.pivot_sql,
            created_at=analysis.created_at,
            updated_at=analysis.updated_at
        )
        for analysis in analyses
    ]