from pydantic import BaseModel, EmailStr, ConfigDict, Field, constr
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from uuid import UUID
from decimal import Decimal


from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class message(BaseModel):
    subject: str
    content: str

class EmailAttachmentCreate(BaseModel):
    filename: str
    content_type: str
    file_path: str
    created_at: Optional[datetime] = None

class Information(BaseModel):
    data: Dict[str, Any]

class RulesCreate(BaseModel):
    max_cx_pii_score: Optional[int] = None
    max_txn_amt_avg: Optional[int] = None
    max_cancelled_txn_cnt_pct: Optional[int] 
    max_card_num_density: Optional[int] = None
    max_curr_diversity_score: Optional[int] = None
    max_customer_density: Optional[int] = None
    max_cx_complaint_txn_pct: Optional[int] = None
    max_day_cos: Optional[int] = None
    max_day_sin: Optional[int] = None
    max_device_id_density: Optional[int] = None
    max_failed_txn_cnt_pct: Optional[int] = None
    max_hour_cos: Optional[int] = None
    max_hour_sin: Optional[int] = None
    max_hrs_since_last_transaction: Optional[int] = None
    max_interntational_txn_cnt_pct: Optional[int] = None
    max_invoice_and_txn_amt_diff_pct: Optional[int] = None
    max_ip_density: Optional[int] = None
    max_late_night_txn_amt_avg: Optional[int] = None
    max_late_night_txn_cnt: Optional[int] = None
    max_month_cos: Optional[int] = None
    max_month_sin: Optional[int] = None
    max_num_distinct_currency_used: Optional[int] = None
    max_chargeback_txn_cnt_pct: Optional[int] = None
    max_name_mismatch_txn_cnt_pct: Optional[int] = None
    max_risky_cx_txn_cnt_pct: Optional[int] = None
    max_round_txn_cnt_pct: Optional[int] = None
    max_txn_cnt: Optional[int] = None
    max_txn_amt_sum: Optional[int] = None
    max_velocity_transaction: Optional[int] = None


class TemporalMetricsCreate(BaseModel):
    data_type: str
    data_value: str
    data_timestamp: datetime

class TemporalMetricsListCreate(BaseModel):
    temporal_metrics: List[TemporalMetricsCreate]

class EmailCommunicationCreate(BaseModel):
    sender: str
    receiver: str
    subject: str
    content: str
    timestamp: str
    chat_id: str

class EmailCommunicationListCreate(BaseModel):
    email_communication: List[EmailCommunicationCreate]

class BooleanValueMerchantCreate(BaseModel):
    merchant_id: UUID
    ip_density_7d: bool
    cx_city: bool
    pct_failed_txn_amt_7d: bool
    is_forged_doc: bool
    is_voice_mismatch: bool
    neg_intent_score: bool
    number_connected_entites: bool
    number_director: bool
    mer_employee_turnover_30d: bool
    num_neg_review_30d: bool


class RuleUpdate(BaseModel):
    status: bool

class RuleStatusCreate(BaseModel):
    rule_status: bool
    rule_threshold: float
    rule_severity: str

class LLMredFlagsStatusCreate(BaseModel):
    rule_number: int
    rule_name: str
    rule_description: str
    status: bool

class ReportGenCreate(BaseModel):
    frontend_component_id: str
    component_type: str 
    data: Dict[str, Any]

class ReportGenListCreate(BaseModel):
    report_gen: List[ReportGenCreate]

class MetricValueMerchantCreate(BaseModel):
    merchant_id: UUID
    ip_density_7d: int
    cx_city: str
    pct_failed_txn_amt_7d: str
    is_forged_doc: bool
    is_voice_mismatch: bool
    neg_intent_score: float
    number_connected_entites: int
    number_director: int
    mer_employee_turnover_30d: float
    num_neg_review_30d: int

class RuleMetricTransactionsCreate(BaseModel):
    ip_density_7d: str
    cx_city: str
    pct_failed_txn_amt_7d: str


class RuleMetricDocumentsCreate(BaseModel):
    is_forged_doc: str
    created_at: str

class RuleMetricCommunicationsCreate(BaseModel):
    is_voice_mismatch: str
    neg_intent_score: str
    created_at: str

class RuleMetricNetworkCreate(BaseModel):
    number_connected_entites: str
    number_director: str
    created_at: str

class RuleMetricLegalAndRegulatoryCreate(BaseModel):
    mer_employee_turnover_30d: str
    created_at: str

class RuleMetricDigitalFootprintCreate(BaseModel):
    num_neg_review_30d: str
    created_at: str

class InvestigatorCreate(BaseModel):
    name: str
    email: str
    current_caseload: int
    expertise: str
    SLA_adherence_percentage: int

class InvestigatorListCreate(BaseModel):
    investigators: List[InvestigatorCreate]

class CaseRelation(BaseModel):
    case_number: str
    related_case_number: str
    relationship_type: str


class InvestigationUpdate(BaseModel):
    assignee_Name: str
    assignee_Email: str
    status: str
    
class NoteCaseEvent(BaseModel):
    title: str
    timestamp: str
    user: str
    description: str

class CaseEventCreate(BaseModel):
    case_event_id: str
    investigation_id: str
    timestamp: str
    type: str
    description: str
    user: str
    content: str

class CaseEventListCreate(BaseModel):
    events: List[CaseEventCreate]

class CaseEventMetaDataCreate(BaseModel):
    case_event_id: str
    channel: str
    oldStatus: str
    newStatus: str
    documentType: str
    communicationType: str

class CaseEventMetaDataListCreate(BaseModel):
    meta_data: List[CaseEventMetaDataCreate]

class RecentMentionCreate(BaseModel):
    merchant_id: UUID
    source: str
    title: str
    date: date
    sentiment: str
    snippet: str

class RecentMentionsListCreate(BaseModel):
    recent_mentions: List[RecentMentionCreate]

class SecurityStatus(BaseModel):
    merchant_id: str
    website_url: str
    security_type: str
    security_status: str

class WebsiteMetricsCreate(BaseModel):
    merchant_id: UUID
    domain: str
    age: int
    monthly_traffic: int
    traffic_trend_value: float
    traffic_trend_positive: bool
    trust_score: int
    security: List[SecurityStatus]
    last_updated: datetime

class WebsiteMetricsListCreate(BaseModel):
    website_metrics: List[WebsiteMetricsCreate]

class ReviewSourcesCreate(BaseModel):
    merchant_id: UUID
    platform: str
    rating: float
    total_reviews: int
    sentiment: str

class ReviewSourcesListCreate(BaseModel):
    review_sources: List[ReviewSourcesCreate]


class SocialPresenceCreate(BaseModel):
    merchant_id: UUID
    platform: str
    icon_type: str
    metrics: int
    followers: int
    posts_monthly: int
    engagement_rate: float
    employee_count: int
    verified: bool

class SocialPresenceListCreate(BaseModel):
    social_presence: List[SocialPresenceCreate]

class MerchantContactBase(BaseModel):
    type: str
    address: str
    contact_person: str
    email: str
    phone: str
    alt_phone: Optional[str] = None
    mobile: Optional[str] = None

class MerchantBankingBase(BaseModel):
    account_number: str
    ifsc: str
    bank_name: str
    account_type: str
    verification_status: str
    settlement_cycle: Optional[str] = None
    rolling_reserve_percentage: Optional[float] = None


class MerchantBase(BaseModel):
    legal_name: str
    trade_name: str
    business_type: str
    incorporation_date: date
    industry: str
    description: Optional[str] = None
    business_category: str
    business_subcategory: str
    business_model: Optional[str] = None
    onboarding_platform: Optional[str] = None

class MerchantCreate(MerchantBase):
    contacts: List[MerchantContactBase]
    banking: MerchantBankingBase

class MerchantResponse(MerchantBase):
    id: UUID
    status: str
    onboarding_date: date
    created_at: datetime
    updated_at: datetime
    contacts: List[MerchantContactBase]
    banking: MerchantBankingBase
    risk_score: Optional[float] = None

    class Config:
        from_attributes = True

class Response(BaseModel):
    status: str
    message: str
    data: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(arbitrary_types_allowed=True)

class TransactionBase(BaseModel):
    transaction_type: str
    merchant_type: str
    amount: float
    transaction_id: str
    merchant_name: str
    risk_score: Optional[int] = None
    risk_description: Optional[str] = None
    status: str
    city: Optional[str] = None
    country_code: Optional[str] = None
    product_name: Optional[str] = None

class TransactionCreate(BaseModel):
    merchant_id: str
    transaction_type: str
    merchant_type: str
    city: str
    payment_channel: str
    country_code: str
    amount: float
    transaction_id: str
    merchant_name: str
    risk_score: int
    risk_description: Optional[str]
    product_name: Optional[str]
    status: str
    timestamp: datetime
    dispute_date: Optional[datetime]
    complain_date: Optional[datetime]
    created_at: Optional[datetime]
    is_fraud_transaction: Optional[bool]
    cx_id: Optional[str]
    cx_ip: Optional[str]
    cx_device_id: Optional[str]
    cx_card_number: Optional[str]
    cx_city: Optional[str]
    cx_pii_linkage_score: Optional[int]
    is_cardholder_name_match: Optional[bool]
    is_chargeback: Optional[bool]
    is_cx_international: Optional[bool]
    txn_status: Optional[str]
    is_cx_risky: Optional[bool]
    invoice_amount: Optional[float]
    is_cancelled: Optional[bool]
    txn_currency: Optional[str]
    has_cx_complaint: Optional[bool]

    model_config = ConfigDict(arbitrary_types_allowed=True)

class TransactionListCreate(BaseModel):
    transactions: List[TransactionCreate]


class TransactionResponse(TransactionBase):
    id: UUID
    merchantId: UUID
    transactionType: str
    merchantType: str
    city: str
    countryCode: str
    amount: float
    transactionId: str
    merchantName: str
    riskScore: int
    riskDescription: Optional[str]
    productName: Optional[str]
    disputeDate: Optional[datetime]
    complainDate: Optional[datetime]
    timestamp: datetime
    status: str
    createdAt: datetime

    class Config:
        from_attributes = True

class TransactionList(BaseModel):
    transactions: List[TransactionResponse]
    pagination: dict

    class Config:
        from_attributes = True

class PaymentChannelDetails(BaseModel):
    url: Optional[str] = None
    integration: Optional[str] = None
    api_key: Optional[str] = None
    merchant_id: Optional[str] = None

class ProcessingLimits(BaseModel):
    daily: float = 0
    monthly: float = 0

class ChannelMetrics(BaseModel):
    success_rate: float = 0
    volume_processed: float = 0

class PaymentChannel(BaseModel):
    id: str
    type: str
    name: str
    status: str
    added_on: str
    details: PaymentChannelDetails


class PaymentChannelListResponse(BaseModel):
    channels: List[PaymentChannel]

class Communication(BaseModel):
    sender_id: str
    receiver_id: str
    type: str
    subject: str
    content: str
    timestamp: str

class CommunicationListResponse(BaseModel):
    communications: List[Communication]

class PaymentChannelCreate(BaseModel):
    merchant_id: str
    id: str
    type: constr(max_length=50)  # website, app, pos
    name: constr(max_length=100)
    status: constr(max_length=50)  # active, inactive
    added_on: str



class PayoutCreate(BaseModel):
    merchant_id: str
    payout_id: str
    amount: int
    status: str
    bank_account: str
    utr: Optional[str] = None
    timestamp : str

class CommunicationCreate(BaseModel):
    sender_id: str
    receiver_id: str
    type: str
    subject: str
    content: str
    timestamp : str

class TimelineEventCreate(BaseModel):
    merchant_id: str
    id: str
    time: datetime
    event: str
    type: str

class RiskIndicatorCreate(BaseModel):
    label: str
    value: str
    severity: str

class RiskCategoryCreate(BaseModel):
    title: str
    score: int = Field(ge=0, le=100)
    description: str
    indicators: List[RiskIndicatorCreate]

class RiskAssessmentCreate(BaseModel):
    percentile: str
    percentile_business_category: str
    risk_level: str
    description: str
    categories: List[RiskCategoryCreate]

class FlagCreate(BaseModel):
    merchant_id: str
    flag_type: str  # transactions, compliance, network
    severity: str
    text: str
    importance: float
    timestamp: Optional[datetime] = None

class CommonConnectionCreate(BaseModel):
    merchant_id: str
    sender_id: str
    receiver_id: str
    connection_type: str
    connection_value: str
    shared_with: str

class CommonConnectionsListCreate(BaseModel):
    connections: List[CommonConnectionCreate]


class NetworkOverviewCreate(BaseModel):
    merchant_id: str
    total_connections: int
    high_risk_connections: int
    network_risk_score: int

class NetworkOverviewListCreate(BaseModel):
    network_overviews: List[NetworkOverviewCreate]


class LinkedEntitiesCreate(BaseModel):
    merchant_id: str
    related_entity_name: str
    relationship_type: str
    registration_number: str
    location: str
    connection_count: int
    risk_level: str


class LinkedEntitiesListCreate(BaseModel):
    linked_entities: List[LinkedEntitiesCreate]


class ComplianceDocUpdate(BaseModel):
    number: Optional[str]
    status: Optional[str]
    lastFiled: Optional[date]
    verificationStatus: Optional[str]



class ProcessingMetricsUpdate(BaseModel):
    monthlyVolume: Optional[Decimal]
    averageTicketSize: Optional[Decimal]
    successRate: Optional[Decimal]
    refundRate: Optional[Decimal]
    chargebackRate: Optional[Decimal]
    disputeRate: Optional[Decimal]

class BankDetailsUpdate(BaseModel):
    accountNumber: Optional[str]
    ifsc: Optional[str]
    bankName: Optional[str]
    accountType: Optional[str]
    verificationStatus: Optional[str]




class BasicInfoUpdate(BaseModel):
    legalName: Optional[str]
    tradeName: Optional[str]
    num_directors: Optional[int]
    businessType: Optional[str]
    domain: Optional[str]
    incorporationDate: Optional[date]
    industry: Optional[str]
    description: Optional[str]
    mcaDescription: Optional[str]
    businessCategory: Optional[str]
    businessSubcategory: Optional[str]
    businessModel: Optional[str]
    onboardingDate: Optional[date]
    onboardingPlatform: Optional[str]
    kycVerificationStatus: Optional[str]
    kycVerificationDate: Optional[date]
    ip_geolocation: Optional[str]

class ContactUpdate(BaseModel):
    registeredAddress: Optional[str]
    operatingAddress: Optional[str]
    contactPerson: Optional[str]
    email: Optional[str]
    phone: Optional[str]
    altPhone: Optional[str]
    mobile: Optional[str]


class ComplianceUpdate(BaseModel):
    businessDocs: Optional[Dict[str, ComplianceDocUpdate]]
    kycStatus: Optional[Dict[str, str]]
    documents: Optional[List[Dict[str, Any]]]

class FinancialUpdate(BaseModel):
    processingMetrics: Optional[ProcessingMetricsUpdate]
    bankDetails: Optional[BankDetailsUpdate]

class MerchantProfileUpdate(BaseModel):
    basicInfo: Optional[BasicInfoUpdate]
    contact: Optional[ContactUpdate]
    onlinePresence: Optional[Dict[str, str]]
    compliance: Optional[ComplianceUpdate]
    financial: Optional[FinancialUpdate]

    model_config = ConfigDict(arbitrary_types_allowed=True)

class MerchantComplianceUpdate(BaseModel):
    businessDocs: Optional[Dict[str, Dict[str, str]]]
    kycStatus: Optional[Dict[str, str]]

    model_config = ConfigDict(arbitrary_types_allowed=True)

class MerchantFinancialsUpdate(BaseModel):
    settlementDetails: Optional[Dict[str, str]]
    revenueMetrics: Optional[Dict[str, str]]

    model_config = ConfigDict(arbitrary_types_allowed=True)

class NetworkOverviewCreate(BaseModel):
    total_connections: int
    high_risk_connections: int
    network_risk_score: int

class LinkedEntityCreate(BaseModel):
    related_entity_name: str
    relationship_type: str
    registration_number: str
    location: str
    connection_count: int
    risk_level: str
    details: List[Dict[str, str]]

class CommonConnectionCreate(BaseModel):
    connection_type: str
    connection_value: str
    shared_with: List[str]

class MerchantLinkageUpdate(BaseModel):
    network_overview: NetworkOverviewCreate
    linked_entities: List[LinkedEntityCreate]
    common_connections: List[CommonConnectionCreate]


class DocumentsUploadedCreate(BaseModel):
    merchant_id: UUID
    document_type: str
    document_number: str
    status: str
    date_of_upload: datetime
    is_forged: bool
    
class DocumentsUploadedListCreate(BaseModel):
    documents_uploaded: List[DocumentsUploadedCreate]


class DevicesUsedCreate(BaseModel):
    merchant_id: UUID
    device_id: str
    ip_address: str
    date_of_addition: str

class DevicesUsedListCreate(BaseModel):
    devices_used: List[DevicesUsedCreate]


class ReviewSourceCreate(BaseModel):
    platform: str
    rating: float
    total_reviews: int
    sentiment: str

class SocialPresenceCreate(BaseModel):
    platform: str
    metrics: Dict[str, Any]

class RecentMentionCreate(BaseModel):
    source: str
    title: str
    date: date
    sentiment: str
    snippet: str

class MerchantDigitalFootprintUpdate(BaseModel):
    website_metrics: WebsiteMetricsCreate
    review_sources: List[ReviewSourceCreate]
    social_presence: List[SocialPresenceCreate]
    recent_mentions: List[RecentMentionCreate]

class KeyMetricsCreate(BaseModel):
    merchant_id: UUID
    date_of_onboarding: date
    average_daily_transactions: float
    average_payout_size: float
    total_amount: float
    total_count: int
    business_type : str
    business_category : str
    total_num_investigations: int
    merchant_legalName: str
    chargeback_percentage: float
    current_balance_in_ledger: float
    account_status: str
    integration_types: str
    no_of_unique_customers: int

class KeyMetricsListCreate(BaseModel):
    key_metrics: List[KeyMetricsCreate]

class InvestigationNoteCreate(BaseModel):
    merchant_id: UUID
    investigation_id: str
    title: str
    description: str
    timestamp: str

class InvestigationCreate(BaseModel):
    merchant_id: UUID 
    investigation_id: str
    case_number: Optional[str] = None
    title: str
    description: str
    status: str
    priority: str
    assignee_Name: Optional[str] = None
    assignee_Email: Optional[str] = None
    merchant_name: str
    sla_deadline: Optional[str] = None
    last_updated: str
    sla_deadline: str
    

class TimelineEventListCreate(BaseModel):
    events: List[TimelineEventCreate]

class PayoutListCreate(BaseModel):
    payouts: List[PayoutCreate]


class PaymentChannelListCreate(BaseModel):
    channels: List[PaymentChannelCreate]


class CommunicationListCreate(BaseModel):
    communications: List[CommunicationCreate]


class FlagListCreate(BaseModel):
    flags: List[FlagCreate]


class InvestigationNoteListCreate(BaseModel):
    notes: List[InvestigationNoteCreate]

class InvestigationListCreate(BaseModel):
    investigations: List[InvestigationCreate]

class UserCreate(BaseModel):
    email: EmailStr
    username: str
    password: str

class UserResponse(BaseModel):
    email: EmailStr
    
class UserAuthenticate(BaseModel):
    email: EmailStr
    password: str
